"use client"

import { useState, useEffect } from "react"
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger } from "@/components/ui/tabs"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Search, Plus, Edit, Trash2, BookOpen, FileText, VideoIcon, Eye } from "lucide-react"
import { getBooks } from "@/app/actions/books"
import { getChapters } from "@/app/actions/chapters"

interface Book {
  id: string
  title: string
  author: string
  type: string
  createdAt: string
}

interface Chapter {
  id: string
  title: string
  bookTitle: string
  chapterNumber: number
}

interface Video {
  id: string
  title: string
  duration: string
  minister: string
  publishedDate: string
}

export default function ContentManagementPage() {
  const [books, setBooks] = useState<Book[]>([])
  const [chapters, setChapters] = useState<Chapter[]>([])
  const [videos, setVideos] = useState<Video[]>([])
  const [searchTerm, setSearchTerm] = useState("")
  const [loading, setLoading] = useState(true)

  // Fallback mock data
  const fallbackBooks: Book[] = [
    {
      id: "1",
      title: "Walking in Faith",
      author: "Rev. Johnson",
      type: "Study Guide",
      createdAt: "2024-01-15T00:00:00Z",
    },
    {
      id: "2",
      title: "The Power of Prayer",
      author: "Rev. Smith",
      type: "Book",
      createdAt: "2024-02-20T00:00:00Z",
    },
    {
      id: "3",
      title: "Understanding Grace",
      author: "Rev. Johnson",
      type: "Study Guide",
      createdAt: "2024-03-10T00:00:00Z",
    },
  ]

  const fallbackChapters: Chapter[] = [
    {
      id: "1",
      title: "Introduction to Faith",
      bookTitle: "Walking in Faith",
      chapterNumber: 1,
    },
    {
      id: "2",
      title: "Building Your Foundation",
      bookTitle: "Walking in Faith",
      chapterNumber: 2,
    },
    {
      id: "3",
      title: "The Nature of Prayer",
      bookTitle: "The Power of Prayer",
      chapterNumber: 1,
    },
  ]

  const fallbackVideos: Video[] = [
    {
      id: "1",
      title: "Sunday Morning Service - Faith and Works",
      duration: "45:30",
      minister: "Rev. Johnson",
      publishedDate: "2024-01-07T00:00:00Z",
    },
    {
      id: "2",
      title: "Wednesday Bible Study - Prayer Life",
      duration: "32:15",
      minister: "Rev. Smith",
      publishedDate: "2024-01-10T00:00:00Z",
    },
    {
      id: "3",
      title: "Special Teaching - Grace and Mercy",
      duration: "38:45",
      minister: "Rev. Johnson",
      publishedDate: "2024-01-14T00:00:00Z",
    },
  ]

  useEffect(() => {
    const fetchContent = async () => {
      try {
        setLoading(true)

        // Fetch books
        const booksData = await getBooks()
        if (booksData && Array.isArray(booksData)) {
          setBooks(
            booksData.map((book) => ({
              id: book.id,
              title: book.title,
              author: book.author || "Unknown Author",
              type: book.type || "Book",
              createdAt: book.createdAt || new Date().toISOString(),
            })),
          )
        } else {
          setBooks(fallbackBooks)
        }

        // Fetch chapters
        const chaptersData = await getChapters()
        if (chaptersData && Array.isArray(chaptersData)) {
          setChapters(
            chaptersData.map((chapter) => ({
              id: chapter.id,
              title: chapter.title,
              bookTitle: chapter.bookTitle || "Unknown Book",
              chapterNumber: chapter.chapterNumber || 1,
            })),
          )
        } else {
          setChapters(fallbackChapters)
        }

        // Set fallback videos (no video action exists yet)
        setVideos(fallbackVideos)
      } catch (error) {
        console.error("Error fetching content:", error)
        // Use fallback data on error
        setBooks(fallbackBooks)
        setChapters(fallbackChapters)
        setVideos(fallbackVideos)
      } finally {
        setLoading(false)
      }
    }

    fetchContent()
  }, [])

  const filteredBooks = books.filter(
    (book) =>
      book.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      book.author.toLowerCase().includes(searchTerm.toLowerCase()),
  )

  const filteredChapters = chapters.filter((chapter) => chapter.title.toLowerCase().includes(searchTerm.toLowerCase()))

  const filteredVideos = videos.filter((video) => video.title.toLowerCase().includes(searchTerm.toLowerCase()))

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <div className="h-8 bg-gray-200 rounded w-64 animate-pulse"></div>
            <div className="h-4 bg-gray-200 rounded w-96 mt-2 animate-pulse"></div>
          </div>
          <div className="h-10 bg-gray-200 rounded w-32 animate-pulse"></div>
        </div>
        <div className="h-64 bg-gray-200 rounded animate-pulse"></div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Content Management</h1>
          <p className="text-gray-600">Manage books, chapters, videos, and other content</p>
        </div>
        <Button className="bg-red-600 hover:bg-red-700">
          <Plus className="w-4 h-4 mr-2" />
          Add Content
        </Button>
      </div>

      <div className="flex items-center space-x-4">
        <div className="relative flex-1 max-w-md">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
          <Input
            placeholder="Search content..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>
      </div>

      <Tabs defaultValue="books" className="space-y-6">
        <TabsList>
          <TabsTrigger value="books">Books ({books.length})</TabsTrigger>
          <TabsTrigger value="chapters">Chapters ({chapters.length})</TabsTrigger>
          <TabsTrigger value="videos">Videos ({videos.length})</TabsTrigger>
        </TabsList>

        <TabsContent value="books">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <BookOpen className="w-5 h-5 mr-2" />
                Books Management
              </CardTitle>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Title</TableHead>
                    <TableHead>Author</TableHead>
                    <TableHead>Type</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Created</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredBooks.map((book) => (
                    <TableRow key={book.id}>
                      <TableCell className="font-medium">{book.title}</TableCell>
                      <TableCell>{book.author}</TableCell>
                      <TableCell>
                        <Badge variant="outline">{book.type}</Badge>
                      </TableCell>
                      <TableCell>
                        <Badge variant="outline" className="bg-green-50 text-green-700">
                          Published
                        </Badge>
                      </TableCell>
                      <TableCell className="text-sm text-gray-500">
                        {new Date(book.createdAt).toLocaleDateString()}
                      </TableCell>
                      <TableCell>
                        <div className="flex space-x-2">
                          <Button size="sm" variant="outline">
                            <Eye className="w-3 h-3" />
                          </Button>
                          <Button size="sm" variant="outline">
                            <Edit className="w-3 h-3" />
                          </Button>
                          <Button size="sm" variant="outline" className="text-red-600">
                            <Trash2 className="w-3 h-3" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="chapters">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <FileText className="w-5 h-5 mr-2" />
                Chapters Management
              </CardTitle>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Title</TableHead>
                    <TableHead>Book</TableHead>
                    <TableHead>Order</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredChapters.map((chapter) => (
                    <TableRow key={chapter.id}>
                      <TableCell className="font-medium">{chapter.title}</TableCell>
                      <TableCell>{chapter.bookTitle}</TableCell>
                      <TableCell>{chapter.chapterNumber}</TableCell>
                      <TableCell>
                        <Badge variant="outline" className="bg-blue-50 text-blue-700">
                          Published
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <div className="flex space-x-2">
                          <Button size="sm" variant="outline">
                            <Eye className="w-3 h-3" />
                          </Button>
                          <Button size="sm" variant="outline">
                            <Edit className="w-3 h-3" />
                          </Button>
                          <Button size="sm" variant="outline" className="text-red-600">
                            <Trash2 className="w-3 h-3" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="videos">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <VideoIcon className="w-5 h-5 mr-2" />
                Videos Management
              </CardTitle>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Title</TableHead>
                    <TableHead>Duration</TableHead>
                    <TableHead>Minister</TableHead>
                    <TableHead>Published</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredVideos.map((video) => (
                    <TableRow key={video.id}>
                      <TableCell className="font-medium">{video.title}</TableCell>
                      <TableCell>{video.duration}</TableCell>
                      <TableCell>{video.minister}</TableCell>
                      <TableCell className="text-sm text-gray-500">
                        {new Date(video.publishedDate).toLocaleDateString()}
                      </TableCell>
                      <TableCell>
                        <div className="flex space-x-2">
                          <Button size="sm" variant="outline">
                            <Eye className="w-3 h-3" />
                          </Button>
                          <Button size="sm" variant="outline">
                            <Edit className="w-3 h-3" />
                          </Button>
                          <Button size="sm" variant="outline" className="text-red-600">
                            <Trash2 className="w-3 h-3" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
