import { type NextRequest, NextResponse } from "next/server"
import { query } from "@/lib/db"

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const search = searchParams.get("search") || ""
    const bookId = searchParams.get("bookId") || ""
    const status = searchParams.get("status") || "all"
    const sortBy = searchParams.get("sortBy") || "title"
    const sortOrder = searchParams.get("sortOrder") || "asc"

    let sql = `
      SELECT c.id, c.title, c.content, c.status, c.created_at, c.updated_at,
             b.title as book_title, b.author as book_author
      FROM chapters c
      LEFT JOIN books b ON c.book_id = b.id
      WHERE 1=1
    `

    const params: any[] = []
    let paramIndex = 1

    // Add search filter
    if (search) {
      sql += ` AND (c.title ILIKE $${paramIndex} OR c.content ILIKE $${paramIndex + 1})`
      params.push(`%${search}%`, `%${search}%`)
      paramIndex += 2
    }

    // Add book filter
    if (bookId) {
      sql += ` AND c.book_id = $${paramIndex}`
      params.push(bookId)
      paramIndex++
    }

    // Add status filter
    if (status !== "all") {
      sql += ` AND c.status = $${paramIndex}`
      params.push(status)
      paramIndex++
    }

    // Add sorting
    const validSortColumns = ["title", "created_at", "updated_at"]
    const sortColumn = validSortColumns.includes(sortBy) ? sortBy : "title"
    const order = sortOrder === "desc" ? "DESC" : "ASC"
    sql += ` ORDER BY c.${sortColumn} ${order}`

    const result = await query(sql, params)

    const chapters = result.rows.map((chapter) => ({
      id: chapter.id,
      title: chapter.title,
      content: chapter.content,
      status: chapter.status,
      book_title: chapter.book_title,
      book_author: chapter.book_author,
      created_at: chapter.created_at,
      updated_at: chapter.updated_at,
    }))

    return NextResponse.json(chapters)
  } catch (error) {
    console.error("Error fetching chapters:", error)
    return NextResponse.json([], { status: 500 })
  }
}
