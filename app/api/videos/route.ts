import { type NextRequest, NextResponse } from "next/server"

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const search = searchParams.get("search") || ""
    const minister = searchParams.get("minister") || "all"
    const sortBy = searchParams.get("sortBy") || "created_at"
    const sortOrder = searchParams.get("sortOrder") || "desc"

    // For now, return mock data since we don't have a videos table yet
    const mockVideos = [
      {
        id: 1,
        title: "Sunday Service - Faith",
        minister: "Pastor <PERSON>",
        duration: "45:32",
        youtube_url: "https://youtube.com/watch?v=example1",
        description: "A powerful message about walking by faith",
        subjects: ["Faith", "Christian Living"],
        created_at: new Date().toISOString(),
      },
      {
        id: 2,
        title: "Prayer Meeting",
        minister: "<PERSON>",
        duration: "32:18",
        youtube_url: "https://youtube.com/watch?v=example2",
        description: "Understanding the power of prayer",
        subjects: ["Prayer", "Spiritual Life"],
        created_at: new Date().toISOString(),
      },
    ]

    let filteredVideos = mockVideos

    // Apply search filter
    if (search) {
      filteredVideos = filteredVideos.filter(
        (video) =>
          video.title.toLowerCase().includes(search.toLowerCase()) ||
          video.minister.toLowerCase().includes(search.toLowerCase()) ||
          video.description.toLowerCase().includes(search.toLowerCase()),
      )
    }

    // Apply minister filter
    if (minister !== "all") {
      filteredVideos = filteredVideos.filter((video) => video.minister === minister)
    }

    return NextResponse.json(filteredVideos)
  } catch (error) {
    console.error("Error fetching videos:", error)
    return NextResponse.json([], { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { title, minister, youtubeUrl, description, subjects } = body

    // For now, just return success since we don't have a videos table
    const mockVideo = {
      id: Date.now(),
      title,
      minister,
      youtube_url: youtubeUrl,
      description,
      subjects: subjects.split(",").map((s: string) => s.trim()),
      duration: "00:00",
      created_at: new Date().toISOString(),
    }

    return NextResponse.json({
      success: true,
      video: mockVideo,
    })
  } catch (error) {
    console.error("Error creating video:", error)
    return NextResponse.json({ success: false, error: "Failed to create video" }, { status: 500 })
  }
}
