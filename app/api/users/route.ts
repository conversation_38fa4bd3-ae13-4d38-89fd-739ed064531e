import { type NextRequest, NextResponse } from "next/server"
import { query } from "@/lib/db"
import bcrypt from "bcryptjs"

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { username, email, fullName, password, role } = body

    // Hash the password
    const hashedPassword = await bcrypt.hash(password, 12)

    // Insert user into database
    const result = await query(
      `INSERT INTO users (username, email, full_name, password_hash, role, created_at, updated_at) 
       VALUES ($1, $2, $3, $4, $5, NOW(), NOW()) 
       RETURNING id, username, email, full_name, role, created_at`,
      [username, email, fullName, hashedPassword, role],
    )

    const user = result.rows[0]

    return NextResponse.json({
      success: true,
      user: {
        id: user.id,
        username: user.username,
        email: user.email,
        fullName: user.full_name,
        role: user.role,
        createdAt: user.created_at,
      },
    })
  } catch (error) {
    console.error("Error creating user:", error)
    return NextResponse.json({ success: false, error: "Failed to create user" }, { status: 500 })
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const search = searchParams.get("search") || ""
    const role = searchParams.get("role") || "all"
    const sortBy = searchParams.get("sortBy") || "created_at"
    const sortOrder = searchParams.get("sortOrder") || "desc"

    let sql = `
      SELECT id, username, email, full_name, role, created_at, updated_at, last_login
      FROM users
      WHERE 1=1
    `

    const params: any[] = []
    let paramIndex = 1

    // Add search filter
    if (search) {
      sql += ` AND (username ILIKE $${paramIndex} OR email ILIKE $${paramIndex + 1} OR full_name ILIKE $${paramIndex + 2})`
      params.push(`%${search}%`, `%${search}%`, `%${search}%`)
      paramIndex += 3
    }

    // Add role filter
    if (role !== "all") {
      sql += ` AND role = $${paramIndex}`
      params.push(role)
      paramIndex++
    }

    // Add sorting
    const validSortColumns = ["username", "email", "full_name", "role", "created_at", "last_login"]
    const sortColumn = validSortColumns.includes(sortBy) ? sortBy : "created_at"
    const order = sortOrder === "desc" ? "DESC" : "ASC"
    sql += ` ORDER BY ${sortColumn} ${order}`

    const result = await query(sql, params)

    const users = result.rows.map((user) => ({
      id: user.id,
      username: user.username,
      email: user.email,
      fullName: user.full_name,
      role: user.role,
      createdAt: user.created_at,
      updatedAt: user.updated_at,
      lastLogin: user.last_login,
    }))

    return NextResponse.json(users)
  } catch (error) {
    console.error("Error fetching users:", error)
    return NextResponse.json([], { status: 500 })
  }
}
