import { type NextRequest, NextResponse } from "next/server"
import { query } from "@/lib/db"

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const search = searchParams.get("search") || ""
    const category = searchParams.get("category") || "all"
    const status = searchParams.get("status") || "all"
    const sortBy = searchParams.get("sortBy") || "title"
    const sortOrder = searchParams.get("sortOrder") || "asc"

    let sql = `
      SELECT id, title, author, category, status, description, tags, created_at, updated_at
      FROM books
      WHERE 1=1
    `

    const params: any[] = []
    let paramIndex = 1

    // Add search filter
    if (search) {
      sql += ` AND (title ILIKE $${paramIndex} OR author ILIKE $${paramIndex + 1} OR description ILIKE $${paramIndex + 2})`
      params.push(`%${search}%`, `%${search}%`, `%${search}%`)
      paramIndex += 3
    }

    // Add category filter
    if (category !== "all") {
      sql += ` AND category = $${paramIndex}`
      params.push(category)
      paramIndex++
    }

    // Add status filter
    if (status !== "all") {
      sql += ` AND status = $${paramIndex}`
      params.push(status)
      paramIndex++
    }

    // Add sorting
    const validSortColumns = ["title", "author", "created_at", "updated_at"]
    const sortColumn = validSortColumns.includes(sortBy) ? sortBy : "title"
    const order = sortOrder === "desc" ? "DESC" : "ASC"
    sql += ` ORDER BY ${sortColumn} ${order}`

    const result = await query(sql, params)

    const books = result.rows.map((book) => ({
      id: book.id,
      title: book.title,
      author: book.author,
      category: book.category,
      status: book.status,
      description: book.description,
      tags: book.tags ? (typeof book.tags === "string" ? JSON.parse(book.tags) : book.tags) : [],
      created_at: book.created_at,
      updated_at: book.updated_at,
    }))

    return NextResponse.json(books)
  } catch (error) {
    console.error("Error fetching books:", error)
    return NextResponse.json([], { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { title, author, type, category, description, tags } = body

    const result = await query(
      `INSERT INTO books (title, author, category, status, description, tags, created_at, updated_at) 
       VALUES ($1, $2, $3, 'Draft', $4, $5, NOW(), NOW()) 
       RETURNING id, title, author, category, status, description, tags, created_at`,
      [title, author, category, description, JSON.stringify(tags?.split(",").map((t: string) => t.trim()) || [])],
    )

    const book = result.rows[0]

    return NextResponse.json({
      success: true,
      book: {
        id: book.id,
        title: book.title,
        author: book.author,
        category: book.category,
        status: book.status,
        description: book.description,
        tags: book.tags ? JSON.parse(book.tags) : [],
        createdAt: book.created_at,
      },
    })
  } catch (error) {
    console.error("Error creating book:", error)
    return NextResponse.json({ success: false, error: "Failed to create book" }, { status: 500 })
  }
}
