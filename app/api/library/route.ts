import { type NextRequest, NextResponse } from "next/server"
import { query } from "@/lib/db"

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const search = searchParams.get("search") || ""
    const type = searchParams.get("type") || "all"
    const category = searchParams.get("category") || "all"
    const status = searchParams.get("status") || "all"
    const sortBy = searchParams.get("sortBy") || "title"
    const sortOrder = searchParams.get("sortOrder") || "asc"

    let sql = `
      SELECT 
        id,
        title,
        'Book' as type,
        author,
        category,
        status,
        description,
        tags,
        created_at,
        updated_at
      FROM books
      WHERE 1=1
    `

    const params: any[] = []
    let paramIndex = 1

    // Add search filter
    if (search) {
      sql += ` AND (title ILIKE $${paramIndex} OR author ILIKE $${paramIndex + 1} OR description ILIKE $${paramIndex + 2})`
      params.push(`%${search}%`, `%${search}%`, `%${search}%`)
      paramIndex += 3
    }

    // Add type filter
    if (type !== "all") {
      // For now, we only have books, but this can be extended
      if (type !== "Book") {
        return NextResponse.json([])
      }
    }

    // Add category filter
    if (category !== "all") {
      sql += ` AND category = $${paramIndex}`
      params.push(category)
      paramIndex++
    }

    // Add status filter
    if (status !== "all") {
      sql += ` AND status = $${paramIndex}`
      params.push(status)
      paramIndex++
    }

    // Add sorting
    const validSortColumns = ["title", "author", "created_at", "updated_at"]
    const sortColumn = validSortColumns.includes(sortBy) ? sortBy : "title"
    const order = sortOrder === "desc" ? "DESC" : "ASC"
    sql += ` ORDER BY ${sortColumn} ${order}`

    const result = await query(sql, params)

    // Parse tags JSON field and ensure all required fields exist
    const books = result.rows.map((book) => ({
      id: book.id,
      title: book.title || "Untitled",
      type: book.type || "Book",
      author: book.author || "Unknown Author",
      category: book.category || "General",
      status: book.status || "Draft",
      description: book.description || "",
      tags: book.tags ? (typeof book.tags === "string" ? JSON.parse(book.tags) : book.tags) : [],
      chapters: 0, // Default value
      duration: null,
      targetAudience: null,
      estimatedTime: null,
      lastUpdated: book.updated_at || book.created_at,
      event: null,
      paperbackLink: null,
      ebookLink: null,
      faithLibraryLink: null,
      amazonLink: null,
      inFaithLibrary: false,
      inApp: false,
      created_at: book.created_at,
      updated_at: book.updated_at,
    }))

    return NextResponse.json(books)
  } catch (error) {
    console.error("Error fetching library content:", error)
    return NextResponse.json([], { status: 500 })
  }
}
