"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { FileText, Search, Plus, Eye, Edit, Calendar, BookOpen } from "lucide-react"
import { getChapters, type Chapter } from "@/lib/data/chapters"
import { getBooks } from "@/lib/data/books"

export default function ChaptersPage() {
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedBook, setSelectedBook] = useState("all")
  const [selectedStatus, setSelectedStatus] = useState("all")
  const [chapters, setChapters] = useState<Chapter[]>([])
  const [books, setBooks] = useState<{ id: number; title: string }[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const fetchBooks = async () => {
      try {
        const fetchedBooks = await getBooks()
        setBooks(fetchedBooks.map((book) => ({ id: book.id, title: book.title })))
      } catch (error) {
        console.error("Error fetching books:", error)
      }
    }

    fetchBooks()
  }, [])

  useEffect(() => {
    const fetchChapters = async () => {
      setLoading(true)
      try {
        const fetchedChapters = await getChapters(searchTerm, selectedBook, selectedStatus)
        setChapters(fetchedChapters)
      } catch (error) {
        console.error("Error fetching chapters:", error)
      } finally {
        setLoading(false)
      }
    }

    fetchChapters()
  }, [searchTerm, selectedBook, selectedStatus])

  return (
    <div className="min-h-screen bg-gray-50">
      <main className="p-6">
        <div className="mb-6">
          <div className="flex items-center justify-between mb-4">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Chapters Library</h1>
              <p className="text-gray-600">Individual lessons and sections within your books</p>
            </div>
            <Button className="bg-blue-600 hover:bg-blue-700">
              <Plus className="w-4 h-4 mr-2" />
              New Chapter
            </Button>
          </div>

          {/* Filters */}
          <div className="flex items-center space-x-4 mb-6">
            <div className="relative flex-1 max-w-md">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <Input
                placeholder="Search chapters..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>

            <Select value={selectedBook} onValueChange={setSelectedBook}>
              <SelectTrigger className="w-48">
                <SelectValue placeholder="Book" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Books</SelectItem>
                {books.map((book) => (
                  <SelectItem key={book.id} value={book.id.toString()}>
                    {book.title}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            <Select value={selectedStatus} onValueChange={setSelectedStatus}>
              <SelectTrigger className="w-32">
                <SelectValue placeholder="Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="Published">Published</SelectItem>
                <SelectItem value="Draft">Draft</SelectItem>
                <SelectItem value="Review">Review</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        {loading ? (
          <div className="flex justify-center items-center h-64">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {chapters.map((chapter) => (
              <Card key={chapter.id} className="hover:shadow-lg transition-shadow">
                <CardHeader>
                  <div className="flex items-start justify-between">
                    <FileText className="w-8 h-8 text-blue-600" />
                    <Badge
                      variant={
                        chapter.status === "Published"
                          ? "default"
                          : chapter.status === "Draft"
                            ? "secondary"
                            : "outline"
                      }
                    >
                      {chapter.status}
                    </Badge>
                  </div>
                  <CardTitle className="text-lg line-clamp-2">{chapter.title}</CardTitle>
                  <div className="flex items-center text-sm text-gray-600">
                    <BookOpen className="w-4 h-4 mr-1" />
                    {chapter.book}
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-gray-500">Author:</span>
                      <span className="font-medium">{chapter.author}</span>
                    </div>

                    <div className="flex items-center justify-between text-sm">
                      <span className="text-gray-500">Words:</span>
                      <span className="font-medium">{chapter.wordCount.toLocaleString()}</span>
                    </div>

                    {chapter.scripture && (
                      <div className="text-sm">
                        <span className="text-gray-500">Scripture:</span>
                        <p className="font-medium text-blue-600">{chapter.scripture}</p>
                      </div>
                    )}

                    <div className="flex items-center text-sm text-gray-500">
                      <Calendar className="w-4 h-4 mr-1" />
                      Updated {chapter.lastUpdated}
                    </div>

                    <div className="flex flex-wrap gap-1">
                      {chapter.subjects.map((subject) => (
                        <Badge key={subject} variant="outline" className="text-xs">
                          {subject}
                        </Badge>
                      ))}
                    </div>

                    <div className="flex space-x-2 pt-2">
                      <Button size="sm" variant="outline" className="flex-1">
                        <Eye className="w-4 h-4 mr-1" />
                        View
                      </Button>
                      <Button size="sm" variant="outline" className="flex-1">
                        <Edit className="w-4 h-4 mr-1" />
                        Edit
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}
      </main>
    </div>
  )
}
