"use client"

import { useState } from "react"
import { ViewToggle } from "@/components/ui/view-toggle"
import { VideosGridView } from "@/components/videos/videos-grid-view"
import { VideosListView } from "@/components/videos/videos-list-view"
import { AddVideoModal } from "@/components/videos/add-video-modal"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Search, Plus } from "lucide-react"

const videos = [
  {
    id: 1,
    title: "Sunday Service - The Power of Faith",
    minister: "Pastor <PERSON>",
    duration: "45:32",
    publishedDate: "2024-01-14",
    transcriptionStatus: "completed",
    subjects: ["Faith", "Healing", "Prayer"],
    thumbnail: "/placeholder.svg?height=120&width=200",
    views: 1250,
    description: "A powerful message about walking in faith and seeing God's healing power manifest.",
  },
  {
    id: 2,
    title: "Midweek Study - Biblical Holiness",
    minister: "<PERSON>",
    duration: "32:18",
    publishedDate: "2024-01-12",
    transcriptionStatus: "in-progress",
    transcriptionProgress: 75,
    subjects: ["Holiness", "Sanctification"],
    thumbnail: "/placeholder.svg?height=120&width=200",
    views: 890,
    description: "Deep dive into what it means to live a holy life according to Scripture.",
  },
  {
    id: 3,
    title: "Youth Service - Walking in Love",
    minister: "Minister Johnson",
    duration: "28:45",
    publishedDate: "2024-01-10",
    transcriptionStatus: "pending",
    subjects: ["Love", "Youth", "Christian Living"],
    thumbnail: "/placeholder.svg?height=120&width=200",
    views: 567,
    description: "Encouraging young people to demonstrate Christ's love in their daily lives.",
  },
]

export default function VideosPage() {
  const [view, setView] = useState<"grid" | "list">("grid")
  const [searchTerm, setSearchTerm] = useState("")
  const [showAddModal, setShowAddModal] = useState(false)
  const [sortBy, setSortBy] = useState("publishedDate")
  const [sortOrder, setSortOrder] = useState<"asc" | "desc">("desc")

  const filteredVideos = videos
    .filter(
      (video) =>
        video.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        video.minister.toLowerCase().includes(searchTerm.toLowerCase()) ||
        video.subjects.some((subject) => subject.toLowerCase().includes(searchTerm.toLowerCase())),
    )
    .sort((a, b) => {
      const aValue = a[sortBy as keyof typeof a]
      const bValue = b[sortBy as keyof typeof b]
      const comparison = aValue < bValue ? -1 : aValue > bValue ? 1 : 0
      return sortOrder === "asc" ? comparison : -comparison
    })

  return (
    <div className="min-h-screen bg-gray-50">
      <main className="p-6">
        <div className="mb-6">
          <div className="flex items-center justify-between mb-4">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Video Library</h1>
              <p className="text-gray-600">Manage sermons, teachings, and spiritual content</p>
            </div>
            <div className="flex items-center space-x-2">
              <ViewToggle view={view} onViewChange={setView} />
              <Button onClick={() => setShowAddModal(true)} className="bg-blue-600 hover:bg-blue-700">
                <Plus className="w-4 h-4 mr-2" />
                Add Video
              </Button>
            </div>
          </div>

          <div className="relative max-w-md">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <Input
              placeholder="Search videos, ministers, or subjects..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
        </div>

        {view === "grid" ? (
          <VideosGridView videos={filteredVideos} />
        ) : (
          <VideosListView
            videos={filteredVideos}
            sortBy={sortBy}
            setSortBy={setSortBy}
            sortOrder={sortOrder}
            setSortOrder={setSortOrder}
          />
        )}
      </main>

      <AddVideoModal open={showAddModal} onOpenChange={setShowAddModal} />
    </div>
  )
}
