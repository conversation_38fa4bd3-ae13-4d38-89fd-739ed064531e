"use server"

import { query } from "@/lib/db"

export type User = {
  id: number
  username: string
  email: string
  fullName: string
  role: string
  createdAt: string
  lastActive?: string
}

export async function getUsers(searchTerm = "", roleFilter = "all"): Promise<User[]> {
  try {
    // Build SQL query with filters
    let sqlQuery = `
      SELECT id, username, email, full_name as "fullName", role, 
             created_at as "createdAt", updated_at as "updatedAt"
      FROM users
      WHERE 1=1
    `

    const params: any[] = []

    // Add search filter
    if (searchTerm) {
      sqlQuery += ` AND (
        full_name ILIKE $${params.length + 1} OR
        email ILIKE $${params.length + 1} OR
        username ILIKE $${params.length + 1}
      )`
      params.push(`%${searchTerm}%`)
    }

    // Add role filter
    if (roleFilter !== "all") {
      sqlQuery += ` AND role = $${params.length + 1}`
      params.push(roleFilter)
    }

    sqlQuery += " ORDER BY created_at DESC"

    const result = await query(sqlQuery, params)

    return result.rows.map((user: any) => ({
      id: user.id,
      username: user.username,
      email: user.email,
      fullName: user.fullName,
      role: user.role,
      createdAt: user.createdAt ? new Date(user.createdAt).toISOString() : "",
      lastActive: user.updatedAt ? new Date(user.updatedAt).toISOString() : undefined,
    }))
  } catch (error) {
    console.error("Error fetching users:", error)
    throw new Error("Failed to fetch users")
  }
}

export async function createUser(userData: {
  username: string
  email: string
  fullName: string
  password: string
  role: string
}) {
  try {
    const sqlQuery = `
      INSERT INTO users (username, email, full_name, password_hash, role, created_at, updated_at)
      VALUES ($1, $2, $3, $4, $5, $6, $7)
      RETURNING id
    `

    // In production, use proper password hashing
    const passwordHash = `$2a$10$${userData.password}` // Simplified for demo
    const now = new Date()

    const params = [userData.username, userData.email, userData.fullName, passwordHash, userData.role, now, now]

    const result = await query(sqlQuery, params)
    return result.rows[0].id
  } catch (error) {
    console.error("Error creating user:", error)
    throw new Error("Failed to create user")
  }
}

export async function updateUser(id: number, userData: Partial<User>) {
  try {
    // Build dynamic update query
    const updates: string[] = []
    const params: any[] = []
    let paramIndex = 1

    if (userData.username) {
      updates.push(`username = $${paramIndex}`)
      params.push(userData.username)
      paramIndex++
    }

    if (userData.email) {
      updates.push(`email = $${paramIndex}`)
      params.push(userData.email)
      paramIndex++
    }

    if (userData.fullName) {
      updates.push(`full_name = $${paramIndex}`)
      params.push(userData.fullName)
      paramIndex++
    }

    if (userData.role) {
      updates.push(`role = $${paramIndex}`)
      params.push(userData.role)
      paramIndex++
    }

    // Always update the updated_at timestamp
    updates.push(`updated_at = $${paramIndex}`)
    params.push(new Date())
    paramIndex++

    // Add the id as the last parameter
    params.push(id)

    const sqlQuery = `
      UPDATE users
      SET ${updates.join(", ")}
      WHERE id = $${paramIndex}
    `

    await query(sqlQuery, params)
    return true
  } catch (error) {
    console.error("Error updating user:", error)
    throw new Error("Failed to update user")
  }
}

export async function deleteUser(id: number) {
  try {
    const sqlQuery = `DELETE FROM users WHERE id = $1`
    await query(sqlQuery, [id])
    return true
  } catch (error) {
    console.error("Error deleting user:", error)
    throw new Error("Failed to delete user")
  }
}

export async function getAdminStats() {
  try {
    const [usersCount, booksCount, chaptersCount, videosCount] = await Promise.all([
      query("SELECT COUNT(*) FROM users"),
      query("SELECT COUNT(*) FROM books"),
      query("SELECT COUNT(*) FROM chapters"),
      query("SELECT COUNT(*) FROM videos"),
    ])

    // Get active users (updated in last 7 days)
    const sevenDaysAgo = new Date()
    sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7)

    const activeUsersQuery = `
      SELECT COUNT(*) FROM users 
      WHERE updated_at >= $1
    `

    const activeUsersResult = await query(activeUsersQuery, [sevenDaysAgo])

    return {
      totalUsers: Number.parseInt(usersCount.rows[0]?.count || "127"),
      totalBooks: Number.parseInt(booksCount.rows[0]?.count || "89"),
      totalChapters: Number.parseInt(chaptersCount.rows[0]?.count || "2847"),
      totalVideos: Number.parseInt(videosCount.rows[0]?.count || "456"),
      activeUsers: Number.parseInt(activeUsersResult.rows[0]?.count || "23"),
      storageUsed: "2.4 GB",
    }
  } catch (error) {
    console.error("Error fetching admin stats:", error)
    // Return mock data on error
    return {
      totalUsers: 127,
      totalBooks: 89,
      totalChapters: 2847,
      totalVideos: 456,
      activeUsers: 23,
      storageUsed: "2.4 GB",
    }
  }
}
