import { revalidatePath } from "next/cache"
import { redirect } from "next/navigation"
import { auth } from "@/lib/auth"
import { z } from "zod"
import { query } from "@/lib/db"

const BookSchema = z.object({
  title: z.string().min(1, "Title is required"),
  author: z.string().min(1, "Author is required"),
  genre: z.string().min(1, "Genre is required"),
})

export type InputType = z.infer<typeof BookSchema>

export async function getBooks(
  searchTerm = "",
  type = "all",
  category = "all",
  status = "all",
  sortBy = "title",
  sortOrder: "asc" | "desc" = "asc",
) {
  try {
    // Build SQL query
    let sqlQuery = `
      SELECT 
        li.id,
        li.title,
        COALESCE(u.full_name, li.author) as author,
        li.type,
        COUNT(c.id) as chapters,
        li.status,
        li.category,
        li.updated_at as "lastUpdated",
        COALESCE(li.cover_image_url, li.cover_image, '/placeholder.svg?height=400&width=300') as cover,
        li.description,
        COALESCE(li.tags, ARRAY[]::text[]) as tags
      FROM 
        library_items li
      LEFT JOIN 
        users u ON li.author_id = u.id
      LEFT JOIN 
        chapters c ON li.id = c.library_item_id
    `

    const whereConditions = []
    const params = []
    let paramIndex = 1

    // Add search filter
    if (searchTerm) {
      whereConditions.push(`(li.title ILIKE $${paramIndex} OR COALESCE(u.full_name, li.author) ILIKE $${paramIndex})`)
      params.push(`%${searchTerm}%`)
      paramIndex++
    }

    // Add type filter
    if (type !== "all") {
      whereConditions.push(`li.type = $${paramIndex}`)
      params.push(type)
      paramIndex++
    }

    // Add category filter
    if (category !== "all") {
      whereConditions.push(`li.category = $${paramIndex}`)
      params.push(category)
      paramIndex++
    }

    // Add status filter
    if (status !== "all") {
      whereConditions.push(`li.status = $${paramIndex}`)
      params.push(status)
      paramIndex++
    }

    // Add WHERE clause if conditions exist
    if (whereConditions.length > 0) {
      sqlQuery += ` WHERE ${whereConditions.join(" AND ")}`
    }

    // Add GROUP BY and ORDER BY
    sqlQuery += `
      GROUP BY li.id, li.title, u.full_name, li.author, li.type, li.status, li.category, li.updated_at, li.cover_image_url, li.cover_image, li.description, li.tags
      ORDER BY ${sortBy === "lastUpdated" ? "li.updated_at" : sortBy === "author" ? "COALESCE(u.full_name, li.author)" : `li.${sortBy}`} ${sortOrder === "desc" ? "DESC" : "ASC"}
    `

    const result = await query(sqlQuery, params)

    if (!result.rows) {
      return []
    }

    const books = result.rows.map((book: any) => ({
      id: book.id,
      title: book.title || "Untitled",
      author: book.author || "Unknown Author",
      type: book.type || "Book",
      chapters: Number.parseInt(book.chapters) || 0,
      status: book.status || "Draft",
      category: book.category || "Uncategorized",
      lastUpdated: book.lastUpdated ? new Date(book.lastUpdated).toISOString().split("T")[0] : "",
      tags: Array.isArray(book.tags) ? book.tags : [],
      cover: book.cover || "/placeholder.svg?height=400&width=300",
      description: book.description || "",
    }))

    return books
  } catch (error) {
    console.error("Error fetching books:", error)
    throw new Error(`Failed to fetch books: ${error instanceof Error ? error.message : "Unknown error"}`)
  }
}

export async function CreateBook(data: InputType) {
  const session = await auth()
  const result = BookSchema.safeParse(data)

  if (!result.success) {
    return {
      errors: result.error.flatten().fieldErrors,
      message: "Missing Fields. Failed to Create Book.",
    }
  }

  const { title, author, genre } = result.data

  let userId

  if (session?.user?.id) {
    userId = session.user.id
  } else {
    return {
      error: "Not authenticated",
      message: "Not authenticated",
    }
  }

  try {
    const createBookQuery = `
      INSERT INTO books (title, author, genre, user_id)
      VALUES ($1, $2, $3, $4)
      RETURNING id
    `

    const values = [title, author, genre, userId]

    const newBook = await query(createBookQuery, values)

    revalidatePath("/dashboard/books")
    return { data: newBook, message: "Created Book." }
  } catch (e: any) {
    return {
      message: "Failed to create book",
    }
  }

  redirect("/dashboard/books")
}
