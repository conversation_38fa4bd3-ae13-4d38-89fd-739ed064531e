import { query } from "@/lib/db"
import { isDatabaseConfigured } from "@/lib/database"

export interface Chapter {
  id: string
  title: string
  bookTitle: string
  bookId: string
  author: string
  status: string
  wordCount: number
  lastUpdated: string
  subjects: string[]
}

export async function getChapters(searchTerm = "", bookId = "all", status = "all"): Promise<Chapter[]> {
  if (!isDatabaseConfigured()) {
    throw new Error("Database is not properly configured. Please check your DATABASE_URL environment variable.")
  }

  try {
    let sql = `
      SELECT 
        c.id,
        c.title,
        li.title as "bookTitle",
        li.id as "bookId",
        COALESCE(u.full_name, li.author) as author,
        c.status,
        c.word_count as "wordCount",
        c.updated_at as "lastUpdated",
        COALESCE(
          ARRAY_AGG(DISTINCT s.name) FILTER (WHERE s.name IS NOT NULL),
          ARRAY[]::text[]
        ) as subjects
      FROM 
        chapters c
      LEFT JOIN 
        library_items li ON c.library_item_id = li.id
      LEFT JOIN 
        users u ON li.author_id = u.id
      LEFT JOIN 
        library_item_subjects lis ON li.id = lis.library_item_id
      LEFT JOIN 
        subjects s ON lis.subject_id = s.id
      WHERE 1=1
    `

    const params: any[] = []
    let paramIndex = 1

    if (searchTerm) {
      sql += ` AND (c.title ILIKE $${paramIndex} OR COALESCE(u.full_name, li.author) ILIKE $${paramIndex} OR EXISTS (
        SELECT 1 FROM library_item_subjects lis2
        JOIN subjects s2 ON lis2.subject_id = s2.id
        WHERE lis2.library_item_id = li.id AND s2.name ILIKE $${paramIndex}
      ))`
      params.push(`%${searchTerm}%`)
      paramIndex++
    }

    if (bookId !== "all") {
      sql += ` AND li.id = $${paramIndex}`
      params.push(Number.parseInt(bookId))
      paramIndex++
    }

    if (status !== "all") {
      sql += ` AND c.status = $${paramIndex}`
      params.push(status)
      paramIndex++
    }

    sql += ` GROUP BY c.id, li.title, li.id, u.full_name, li.author ORDER BY c.updated_at DESC`

    const result = await query(sql, params)

    if (!result.rows) {
      return []
    }

    const chapters = result.rows.map((chapter: any) => ({
      id: chapter.id.toString(),
      title: chapter.title || "Untitled Chapter",
      bookTitle: chapter.bookTitle || "Unknown Book",
      bookId: chapter.bookId?.toString() || "",
      author: chapter.author || "Unknown Author",
      status: chapter.status || "Draft",
      wordCount: Number.parseInt(chapter.wordCount) || 0,
      lastUpdated: chapter.lastUpdated ? new Date(chapter.lastUpdated).toISOString().split("T")[0] : "",
      subjects: Array.isArray(chapter.subjects) ? chapter.subjects.filter(Boolean) : [],
    }))

    return chapters
  } catch (error) {
    console.error("Error fetching chapters:", error)
    throw new Error(`Failed to fetch chapters: ${error instanceof Error ? error.message : "Unknown error"}`)
  }
}
