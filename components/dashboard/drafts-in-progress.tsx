import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Tit<PERSON> } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Edit, FileText, Clock } from "lucide-react"

const drafts = [
  {
    id: 1,
    title: "The Power of Prayer",
    type: "Chapter",
    lastEdited: "2 hours ago",
    progress: 75,
  },
  {
    id: 2,
    title: "Foundations Study Guide",
    type: "Book",
    lastEdited: "1 day ago",
    progress: 45,
  },
  {
    id: 3,
    title: "Sermon Notes - Faith",
    type: "Notes",
    lastEdited: "3 days ago",
    progress: 20,
  },
]

export function DraftsInProgress() {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center">
          <FileText className="w-5 h-5 mr-2" />
          Drafts in Progress
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-3">
        {drafts.map((draft) => (
          <div key={draft.id} className="flex items-center justify-between p-3 rounded-lg border hover:bg-gray-50">
            <div className="flex-1">
              <h4 className="text-sm font-medium text-gray-900">{draft.title}</h4>
              <div className="flex items-center space-x-2 mt-1">
                <span className="text-xs text-gray-500">{draft.type}</span>
                <Clock className="w-3 h-3 text-gray-400" />
                <span className="text-xs text-gray-500">{draft.lastEdited}</span>
              </div>
              <div className="mt-2">
                <div className="flex items-center space-x-2">
                  <div className="flex-1 bg-gray-200 rounded-full h-1.5">
                    <div className="bg-indigo-600 h-1.5 rounded-full" style={{ width: `${draft.progress}%` }}></div>
                  </div>
                  <span className="text-xs text-gray-500">{draft.progress}%</span>
                </div>
              </div>
            </div>
            <Button size="sm" variant="outline">
              <Edit className="w-4 h-4 mr-1" />
              Edit
            </Button>
          </div>
        ))}
      </CardContent>
    </Card>
  )
}
