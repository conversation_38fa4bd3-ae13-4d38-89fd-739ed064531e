import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON>eader, <PERSON>Title } from "@/components/ui/card"
import { <PERSON><PERSON><PERSON>, FileText, Video, Tags } from "lucide-react"

const stats = [
  {
    title: "Total Books",
    value: "127",
    change: "+12 this month",
    icon: BookOpen,
    color: "text-blue-600",
  },
  {
    title: "Chapters",
    value: "2,847",
    change: "+89 this week",
    icon: FileText,
    color: "text-green-600",
  },
  {
    title: "Videos",
    value: "456",
    change: "+5 this week",
    icon: Video,
    color: "text-purple-600",
  },
  {
    title: "Subjects",
    value: "234",
    change: "+3 new tags",
    icon: Tags,
    color: "text-orange-600",
  },
]

export function QuickStats() {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      {stats.map((stat) => (
        <Card key={stat.title} className="hover:shadow-md transition-shadow">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-gray-600">{stat.title}</CardTitle>
            <stat.icon className={`w-4 h-4 ${stat.color}`} />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stat.value}</div>
            <p className="text-xs text-gray-500 mt-1">{stat.change}</p>
          </CardContent>
        </Card>
      ))}
    </div>
  )
}
