import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Clock, User, BookOpen, Video } from "lucide-react"

const activities = [
  {
    id: 1,
    type: "chapter",
    title: "Faith and Healing - Chapter 12",
    user: "<PERSON>",
    action: "updated",
    time: "2 hours ago",
    icon: BookOpen,
  },
  {
    id: 2,
    type: "video",
    title: "Sunday Service - Redemption",
    user: "<PERSON>",
    action: "transcribed",
    time: "4 hours ago",
    icon: Video,
  },
  {
    id: 3,
    type: "book",
    title: "Foundations of Faith",
    user: "<PERSON>",
    action: "published",
    time: "1 day ago",
    icon: BookOpen,
  },
]

export function RecentActivity() {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center">
          <Clock className="w-5 h-5 mr-2" />
          Recent Activity
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {activities.map((activity) => (
          <div key={activity.id} className="flex items-center space-x-3 p-3 rounded-lg hover:bg-gray-50">
            <activity.icon className="w-5 h-5 text-gray-400" />
            <div className="flex-1 min-w-0">
              <p className="text-sm font-medium text-gray-900 truncate">{activity.title}</p>
              <div className="flex items-center space-x-2 mt-1">
                <User className="w-3 h-3 text-gray-400" />
                <span className="text-xs text-gray-500">{activity.user}</span>
                <Badge variant="secondary" className="text-xs">
                  {activity.action}
                </Badge>
              </div>
            </div>
            <span className="text-xs text-gray-400">{activity.time}</span>
          </div>
        ))}
      </CardContent>
    </Card>
  )
}
