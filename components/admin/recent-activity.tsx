import { <PERSON>, CardContent, <PERSON>Header, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Activity, User, BookOpen, Video, FileText } from "lucide-react"

const activities = [
  {
    id: 1,
    type: "user_created",
    description: "New user registered: <EMAIL>",
    timestamp: "2 minutes ago",
    icon: User,
    color: "text-blue-600",
  },
  {
    id: 2,
    type: "book_published",
    description: "Book 'Foundations of Faith' was published",
    timestamp: "15 minutes ago",
    icon: BookOpen,
    color: "text-green-600",
  },
  {
    id: 3,
    type: "video_uploaded",
    description: "New video uploaded: 'Sunday Service - Faith'",
    timestamp: "1 hour ago",
    icon: Video,
    color: "text-purple-600",
  },
  {
    id: 4,
    type: "chapter_updated",
    description: "Chapter 'Walking by Faith' was updated",
    timestamp: "2 hours ago",
    icon: FileText,
    color: "text-orange-600",
  },
]

export function RecentActivity() {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center">
          <Activity className="w-5 h-5 mr-2" />
          Recent Activity
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {activities.map((activity) => (
          <div key={activity.id} className="flex items-start space-x-3 p-3 rounded-lg hover:bg-gray-50">
            <div className="p-2 bg-gray-100 rounded-lg">
              <activity.icon className={`w-4 h-4 ${activity.color}`} />
            </div>
            <div className="flex-1 min-w-0">
              <p className="text-sm text-gray-900">{activity.description}</p>
              <p className="text-xs text-gray-500 mt-1">{activity.timestamp}</p>
            </div>
            <Badge variant="outline" className="text-xs">
              {activity.type.replace("_", " ")}
            </Badge>
          </div>
        ))}
      </CardContent>
    </Card>
  )
}
