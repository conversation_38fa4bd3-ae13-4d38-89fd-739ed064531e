import { Card, CardContent, Card<PERSON>eader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { Server, Database, Cpu, HardDrive } from "lucide-react"

export function SystemHealth() {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center">
          <Server className="w-5 h-5 mr-2" />
          System Health
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Database className="w-4 h-4 text-blue-600" />
              <span className="text-sm">Database</span>
            </div>
            <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
              Healthy
            </Badge>
          </div>

          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Cpu className="w-4 h-4 text-orange-600" />
                <span className="text-sm">CPU Usage</span>
              </div>
              <span className="text-sm text-gray-600">23%</span>
            </div>
            <Progress value={23} className="h-2" />
          </div>

          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <HardDrive className="w-4 h-4 text-purple-600" />
                <span className="text-sm">Storage</span>
              </div>
              <span className="text-sm text-gray-600">2.4 GB / 100 GB</span>
            </div>
            <Progress value={2.4} className="h-2" />
          </div>

          <div className="flex items-center justify-between">
            <span className="text-sm">API Status</span>
            <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
              Online
            </Badge>
          </div>

          <div className="flex items-center justify-between">
            <span className="text-sm">Backup Status</span>
            <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
              Last: 2 hours ago
            </Badge>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
